// Helper: get last value for a field by name
function last(fieldName) {
    const frame = data.series?.[0];
    if (!frame) return undefined; 
    const f = frame.fields.find(x => x.name === fieldName);
    return f ? f.values.get(f.values.length - 1) : undefined;
}
  
  // Decide the alert condition.
  const time = last('time');
  const booleanBandValue = last('UrbanGrid AspenRoad');
  const quality = String(last('quality') || '');
  const isRed = booleanBandValue;

  // pick up nodes directly from the inline SVG
  const rect = document.querySelector('svg #statusRect');
  const label = document.querySelector('svg #statusText');
  
  // Colors
  const RED = '#dc2626';
  const GREEN = '#16a34a';
  
  // style + text updates
  if (rect) {
    rect.setAttribute('fill', isRed ? RED : GREEN);
    rect.setAttribute('class', isRed ? "blink" : "");
  }
  if (label) {
    label.textContent = isRed ? 'Out of Band' : 'In Band';
  }