// Helper: get last value for a field by name
function last(fieldName) {
  const frame = data.series?.[0];
  if (!frame) return undefined; 
  const f = frame.fields.find(x => x.name === fieldName);
  return f ? f.values.get(f.values.length - 1) : undefined;
} 

// Decide the alert condition.
const time = last('time'); 
const booleanBandValue = last('/UrbanGrid/JonesFarm/Test/Voltage Band Test');
const quality = String(last('quality') || '');
const isRed = booleanBandValue;

// pick up nodes directly from the inline SVG
const rect = svgmap.rectangle;
const label = svgmap.label; 

// Colors
const RED = '#c4162a';
const GREEN = '#37872d';

console.log("isRed", isRed);
console.log("svgmap", svgmap);

// style + text updates
if (rect) {
  rect.fill(isRed ? RED : GREEN);
  rect.addClass(isRed ? "blink" : "");
}
if (label) {
  label.text(isRed ? 'Out of Band' : 'In Band');
}