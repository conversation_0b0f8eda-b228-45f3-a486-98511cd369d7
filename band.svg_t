<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 200" preserveAspectRatio="xMidYMid slice" style="position:absolute; inset:0; width:100%; height:100%; display:block">
  <style><![CDATA[
    @keyframes hardBlink { 0%,49% {opacity:1} 50%,100% {opacity:0} }
    .blink { animation: hardBlink 800ms steps(1, end) infinite; }
    .label { font: 700 36px Inter, Segoe UI, Arial, sans-serif; fill:#fff }
  ]]></style>

  <!-- one rect we recolor -->
  <rect id="statusRect" class="" x="10" y="10" width="300" height="180" fill="#16a34a"/>
  <text id="statusText" class="label" x="160" y="120" text-anchor="middle">In Band</text>
</svg>