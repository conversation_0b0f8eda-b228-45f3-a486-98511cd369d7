<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 320 200" preserveAspectRatio="xMidYMid slice" style="position:absolute; inset:0; width:100%; height:100%; display:block">
  <style><![CDATA[
    @keyframes hardBlink { 0%,49% {opacity:1} 50%,100% {opacity:0} }
    .blink { animation: hardBlink 800ms steps(1, end) infinite; }
    .label { font-size: 36px; font-weight: 700; font-family: Inter, Segoe UI, Arial, sans-serif; fill:#fff }
  ]]></style>

  <!-- one rect we recolor -->
  <text id="statusText1" name="label" class="label" x="150" y="110" text-anchor="middle"/>
  <rect id="statusRect1" name="rectangle" class="" x="0" y="0" width="300" height="180" fill="#37872d"/>
</svg>