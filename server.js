const http = require('http');
let state = false;

setInterval(() => { state = !state; }, 5000);

const server = http.createServer((req, res) => {
  if (req.url.startsWith('/state')) {
    const now = Date.now();
    const body = [
      {
        time: now,
        "UrbanGrid AltonPostOffice": state,
        quality: state ? "BAD" : "GOOD"
      }
    ];
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify(body));
  } else {
    res.writeHead(404).end();
  }
});
server.listen(4000, () =>
  console.log('Dummy server: http://localhost:4000/state')
);

